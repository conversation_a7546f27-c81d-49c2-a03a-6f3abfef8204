"""
Implements main window of gxpy.
"""

import re
import os
import logging
import datetime
import configparser

from PyQt5.QtCore import pyqtSlot, Qt, QTimer
from PyQt5 import QtCore, QtGui, QtWidgets, uic
from PyQt5.uic import loadUi
from PyQt5.QtGui import QCursor,  QPalette, QPixmap
from PyQt5.QtWidgets import QFileDialog, QMainWindow, QMessageBox, QApplication
from PyQt5.QtWidgets import (QWidget, QTreeWidget, QHBoxLayout, QVBoxLayout, QStyleFactory, QTreeWidgetItem)
from PyQt5.QtWidgets import QApplication, QTreeWidget, QTreeWidgetItem, QHeaderView

from gxpy.gxengine.eventengine import Event, EventEngine, BaseData, EVENT_TIMER
from gxpy.gxengine.mainengine import MainEngine
from gxpy.gxengine.common import resource_path
from gxpy.gxengine.emailengine import EVENT_EMAIL, EmailData

from gxapp.gxtreemanager import GxTreeManager
from gxapp.datachange import DataChange
from gxapp.gxhelp import GxHelp
from gxapp.gxabout import GxAbout
from gxapp.gxsetting import GxSetting
from gxapp.gxrfregister import GxRfRegister
from gxapp.version import version

from gxapp.logengine import LogData, LogDirData, EVENT_LOG, EVENT_LOG_SET_DIR
from gxapp.bttengine_run_data import (
    EVENT_BTT_RUN_BTT,
    EVENT_BTT_KILL_PROCESS,
    EVENT_BTT_RUNTIME_LOG,
    EVENT_BTT_FINISH_LOG,

    BttRunData,
    BttKillProcessData,
    BttRunTimeLogData,
    BttFinishLogData,
)


class MainWindow(QtWidgets.QMainWindow):
    """
    Main window of gxpy.
    """
    process_time_signal = QtCore.pyqtSignal()
    process_log_signal = QtCore.pyqtSignal(LogData)
    btt_process_runtime_log_signal = QtCore.pyqtSignal(BttRunTimeLogData)
    btt_process_finish_log_signal = QtCore.pyqtSignal(BttFinishLogData)

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        """"""
        super(MainWindow, self).__init__()
        self.main_engine = main_engine
        self.event_engine = event_engine
        self.init_data()
        self.register_event()
        self.init_ui()

    def register_event(self):
        """"""
        self.process_time_signal.connect(self.do_process_time)
        self.event_engine.register(EVENT_TIMER, self.process_time_event)

        self.btt_process_runtime_log_signal.connect(self.do_process_runtime_log)
        self.btt_process_finish_log_signal.connect(self.do_process_finish_log)

        self.process_log_signal.connect(self.do_process_log)
        self.event_engine.register(EVENT_LOG, self.process_log_event)

        self.event_engine.register(EVENT_BTT_RUNTIME_LOG, self.btt_process_runtime_log_event)
        self.event_engine.register(EVENT_BTT_FINISH_LOG, self.btt_process_finish_log_event)

    def second_to_str(self, seconds: int):
        m, s = divmod(seconds, 60)
        h, m = divmod(m, 60)
        return "%02d:%02d:%02d" % (h, m, s)

    def process_time_event(self, event: Event):
        self.process_time_signal.emit()

    def do_process_time(self):
        if self.is_test_active is True:
            self.test_time_seconds += 1
            self.ui.labelTestTime.setText(self.second_to_str(self.test_time_seconds))

    def process_log_event(self, event: Event):
        self.process_log_signal.emit(event.data)

    def do_process_log(self, log: LogData):
        date_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        if log.level == logging.ERROR:
            level_name = "ERROR"
        elif log.level == logging.DEBUG:
            level_name = "DEBUG"
            return
        else:
            level_name = "INFO"
        msg = "[{0}] [{1}] {2}".format(date_time, level_name, log.msg)
        linetext = self.span_style[level_name]
        linetext += msg.replace("<", "＜").replace(">", "＞")
        linetext += "</span>"
        self.__update_log(linetext)

    def __update_log(self, log: str):
        self.log_text.append(log)
        self.log_text = self.log_text[-5000:]
        if self.textbrowser_lines_counter > 5000:
            self.ui.textBrowserLog.clear()
            self.textbrowser_lines_counter = 0
            for i in self.log_text:
                self.ui.textBrowserLog.append(i)
                self.textbrowser_lines_counter += 1
        else:
            self.ui.textBrowserLog.append(log)
            self.textbrowser_lines_counter += 1
        self.ui.textBrowserLog.moveCursor(QtGui.QTextCursor.End)

    def btt_process_runtime_log_event(self, event: Event):
        """
        Process run time log event.
        """
        data = event.data
        self.btt_process_runtime_log_signal.emit(data)

    def do_process_runtime_log(self, data: BttRunTimeLogData):
        pass

    def btt_process_finish_log_event(self, event: Event):
        """
        Process finish log event.
        """
        data = event.data
        self.btt_process_finish_log_signal.emit(data)

    def do_process_finish_log(self, data: BttFinishLogData):
        if data.result is False:
            self.write_error_log("测试终止！测试错误：{}".format(data.msg))
        else:
            self.write_info_log("测试完成！总共测试耗时：{}".format(self.second_to_str(self.test_time_seconds)))

        self.btt_kill_process()
        # self.send_email(result=data.result , msg=data.msg)
        self.enable_ui(True)
        self.ui.pushButtonStart.setEnabled(True)
        self.ui.pushButtonStop.setEnabled(False)
        self.tree_manager.set_checkbox_state(True)
        self.is_test_active = False

    def btt_run(self, data: dict):
        """
        Put btt run event with specific message.
        """
        d = BttRunData(data=data)
        event = Event(EVENT_BTT_RUN_BTT, d)
        self.event_engine.put(event)

    def btt_kill_process(self):
        """
        Put btt kill process event with specific message.
        """
        data = BttKillProcessData()
        event = Event(EVENT_BTT_KILL_PROCESS, data)
        self.event_engine.put(event)

    def set_log_dir(self, log_dir: str):
        """
        set log dir.
        """
        log = LogDirData(log_dir=log_dir)
        event = Event(EVENT_LOG_SET_DIR, log)
        self.event_engine.put(event)

    def write_error_log(self, msg: str):
        """
        Put log event with specific message.
        """
        log = LogData(msg=msg, level=logging.ERROR)
        event = Event(EVENT_LOG, log)
        self.event_engine.put(event)

    def write_info_log(self, msg: str):
        """
        Put log event with specific message.
        """
        log = LogData(msg=msg, level=logging.INFO)
        event = Event(EVENT_LOG, log)
        self.event_engine.put(event)

    def write_debug_log(self, msg: str):
        """
        Put log event with specific message.
        """
        log = LogData(msg=msg, level=logging.DEBUG)
        event = Event(EVENT_LOG, log)
        self.event_engine.put(event)

    def show_error_message(self, title: str, message: str, detailed_msg: str = None):
        mb = QMessageBox()
        mb.setIcon(QMessageBox.Critical)
        mb.setWindowTitle(title)
        mb.setText(message)
        mb.setStandardButtons(QMessageBox.Cancel)
        buttonX = mb.button(QMessageBox.Cancel)
        buttonX.setText("OK")
        if detailed_msg != None:
            mb.setDetailedText(detailed_msg)
        mb.exec()

    def init_ui(self, *args):
        self.ui = uic.loadUi(resource_path('gxapp/ui/mainwindow.ui'), self)
        title = "蓝牙自动化测试工具 V" + version
        self.ui.setWindowTitle(title)
        self.menu_init()
        self.enable_ui(True)
        self.ui.pushButtonStart.setEnabled(True)
        self.ui.pushButtonStop.setEnabled(False)
        self.connect_ui()
        self.config_init()

    def connect_ui(self):
        self.ui.pushButtonStart.clicked.connect(self.on_start)
        self.ui.pushButtonStop.clicked.connect(self.on_stop)
        self.ui.pushButtonRfRegister.clicked.connect(self.on_rf_register)
        self.ui.pushButtonClearLog.clicked.connect(self.on_clear_log)

    def enable_ui(self, is_enable: bool):
        if is_enable:
            pass
        else:
            pass

    def menu_init(self):
        # 正确获取菜单栏对象
        self.menubar = self.menuBar()

        # macOS特殊处理
        import sys
        if sys.platform == 'darwin':  # macOS
            # 设置菜单栏为原生macOS样式
            self.menubar.setNativeMenuBar(True)

        # 创建文件菜单
        self.file_menu = self.menubar.addMenu("文件")
        open_action = self.file_menu.addAction("打开测试文件")
        open_action.triggered.connect(self.open_test_case_file)

        # 创建配置菜单
        self.config_menu = self.menubar.addMenu("配置")
        setting_action = self.config_menu.addAction("设置")
        setting_action.triggered.connect(self.setting)

        # 创建帮助菜单
        self.help_menu = self.menubar.addMenu("帮助")
        help_doc_action = self.help_menu.addAction("帮助文档")
        help_doc_action.triggered.connect(self.help_doc)
        about_action = self.help_menu.addAction("关于")
        about_action.triggered.connect(self.help_about)

    def config_init(self):
        self.ip_addr_data = {}

        config = configparser.ConfigParser()
        config.read('config.ini')

        for key, value in config['base'].items():
            if key == 'dc_power':
                if not self.check_ip(value):
                    self.write_error_log("{} IP地址配置错误!".format(key))
                    self.ip_addr_data["dc_power"] = None
                else:
                    self.ip_addr_data["dc_power"] = value
            elif key == 'spectrum_analyzer':
                if not self.check_ip(value):
                    self.write_error_log("{} IP地址配置错误!".format(key))
                    self.ip_addr_data["spectrum_analyzer"] = None
                else:
                    self.ip_addr_data["spectrum_analyzer"] = value
            elif key == 'signal_generator':
                if not self.check_ip(value):
                    self.write_error_log("{} IP地址配置错误!".format(key))
                    self.ip_addr_data["signal_generator"] = None
                else:
                    self.ip_addr_data["signal_generator"] = value
            elif key == 'bluetooth_tester':
                if not self.check_ip(value):
                    self.write_error_log("{} IP地址配置错误!".format(key))
                    self.ip_addr_data["bluetooth_tester"] = None
                else:
                    self.ip_addr_data["bluetooth_tester"] = value

    def parse_testcase_file_data(self, file_path: str):
        pass

    def open_test_case_file(self):
        """
        self.test_case_data = {
            "chip": "APUS",
            "test_case": {
                "RF发射测试": [
                    {"name": "RF发射测试1", "file": "./test/test1.py"},
                    {"name": "RF发射测试2", "file": "./test/test2.py"},
                    {"name": "RF发射测试3", "file": "./test/test3.py"}
                    ],
                "RF接收测试": [
                    {"name": "RF接收测试1", "file": "./test/test1.py"},
                    {"name": "RF接收测试2", "file": "./test/test2.py"},
                    {"name": "RF接收测试3", "file": "./test/test3.py"}
                    ],
                }
            }
        """
        file_path, file_type = QFileDialog.getOpenFileName(
            self, "选取文件", os.path.abspath('.'), "*.xlsx;(*.xlsx)", options=None)
        if file_path != '':     # 打开选择框，但是没有选择文件时，不打印路径信息
            print(file_path)
        # 解析测试汇总文件
        dc = DataChange(filename=file_path)
        ret, data, self.test_case_data = dc.read_test_case()
        if ret is False:
            if data is None:    # 打开选择框，但是没有选择文件时，不显示error信息
                return
            if isinstance(data, str):   # 文件中存在单个错误
                self.write_error_log(msg=data)
            else:
                for error in data:      # 文件中存在多个错误，一次执行，打印全部错误信息，方便修改
                    self.write_error_log(msg=error)
            return
        self.tree_init()

    def tree_init(self):
        """
        """
        self.tree_manager = GxTreeManager(self.ui.treeWidgetTestCase, self.test_case_data)
        self.tree_manager.init_tree_structure()

    def init_data(self):
        self.log_text = []
        self.textbrowser_lines_counter = 0
        self.span_style = {
                "INFO": "<span style=\" font-size:12pt; font-weight:300; color:#000000;\" >",   # black
                "DEBUG": "<span style=\" font-size:12pt; font-weight:300; color:#FFA500;\" >",  # orange
                "ERROR": "<span style=\" font-size:12pt; font-weight:300; color:#FF0000;\" >",  # red
                }
        self.is_test_active = False
        self.test_time_seconds = 0
        self.data = {
                "dc_power": "",
                "spectrum_analyzer": "",
                "signal_generator": "",
                "bluetooth_tester": "",
                "uart_port": "",
                "line_loss": "",
                "save_report_path": "",
                "test_case": [],
                "chip": ""
                }
        self.setting_data={}
        self.test_case_data = {
                "chip": "",
                "test_case": {}
                }

    def check_ip(self, ipaddr: str):
        compile_ip = re.compile(
            r'^(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|[1-9])\.'
            r'(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.'
            r'(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)\.'
            r'(1\d{2}|2[0-4]\d|25[0-5]|[1-9]\d|\d)$')
        if compile_ip.match(ipaddr):
            return True
        else:
            return False

    def pack_run_data(self):
        self.data["dc_power"] = self.ip_addr_data["dc_power"]
        self.data["spectrum_analyzer"] = self.ip_addr_data["spectrum_analyzer"]
        self.data["signal_generator"] = self.ip_addr_data["signal_generator"]
        self.data["bluetooth_tester"] = self.ip_addr_data["bluetooth_tester"]

        if self.setting_data == {}:
            self.write_error_log("请先配置运行参数!")
            return False

        uart_port = self.setting_data["uart_port"]
        if uart_port == "":
            self.write_error_log("串口配置错误!")
            return False
        self.data["uart_port"] = uart_port

        line_loss = self.setting_data["line_loss"]
        if line_loss == "":
            self.write_error_log("线损配置错误!")
            return False
        self.data['line_loss'] = line_loss

        save_report_path = self.setting_data["save_report_path"]
        if save_report_path == "":
            self.write_error_log("测试报告保存路径未配置!")
            return False
        self.data["save_report_path"] = save_report_path
        # filename_with_extension = os.path.basename(save_report_path)
        # filename, extension = os.path.splitext(filename_with_extension)
        # self.set_log_dir(filename)

        checked_items = self.tree_manager.get_checked_items()
        self.data['test_case'] = checked_items

        # 检查是否标记了用例
        total = 0
        for k, v in checked_items.items():
            total += len(v)
        if total == 0:
            self.write_error_log("请选择并勾选要测试的用例！")
            return False

        self.data["chip"] = self.test_case_data["chip"]
        print(self.data)

        return True

    def on_start(self):
        self.test_time_seconds = 0
        if self.pack_run_data() is True:
            self.write_info_log("测试开始!")
            self.is_test_active = True
            self.btt_run(data=self.data)
            self.enable_ui(False)
            self.tree_manager.set_checkbox_state(False)
            self.ui.pushButtonStart.setEnabled(False)
            self.ui.pushButtonStop.setEnabled(True)
            self.ui.pushButtonRfRegister.setEnabled(False)

    def on_stop(self):
        self.write_info_log("测试停止!")
        self.is_test_active = False
        self.btt_kill_process()
        self.enable_ui(True)
        self.tree_manager.set_checkbox_state(True)
        self.ui.pushButtonStart.setEnabled(True)
        self.ui.pushButtonStop.setEnabled(False)
        self.ui.pushButtonRfRegister.setEnabled(True)

    def on_clear_log(self):
        self.ui.textBrowserLog.clear()
        self.log_text = []
        self.textbrowser_lines_counter = 0

    def get_time_str(self):
        # [2021-08-05 10:46:43,672]
        return datetime.datetime.now().strftime("[%Y-%m-%d %H:%M:%S,%f")[:-3]+'] '

    def send_email(self, result: bool, msg: str):
        if not self.email:
            return
        if result is False:
            content = "{} 测试终止! 测试错误:{}".format(self.get_time_str(), msg)
        else:
            content = "{} 测试完成! ".format(self.get_time_str())

        attachment = list()

        receivers = self.email.split(';')
        email = EmailData(receiver=receivers, subject="btttest 测试结果", content=content, attachment=attachment)
        event = Event(EVENT_EMAIL, email)
        self.event_engine.put(event)

    def closeEvent(self, event):
        """
        Call main engine close function before exit.
        """
        event.ignore()
        self.main_engine.close()
        event.accept()

    def help_about(self):
        dlg = GxAbout()
        result = dlg.exec()

    def help_doc(self):
        dlg = GxHelp()
        result = dlg.exec()

    def setting(self):
        dlg = GxSetting(self.ip_addr_data, self.setting_data, self.is_test_active, self.event_engine)
        result = dlg.exec()                             # 显示对话框并等待用户关闭它，获取对话框的返回值(result==1)
        self.setting_data = dlg.setting_data
        print(dlg.setting_data)

    def on_rf_register(self):
        if self.setting_data == {}:
            self.write_error_log("请先配置运行参数!")
            return False

        uart_port = self.setting_data["uart_port"]
        if uart_port == "":
            self.write_error_log("串口配置错误!")
            return False

        dlg = GxRfRegister(self.event_engine, uart_port)
        dlg.exec()

