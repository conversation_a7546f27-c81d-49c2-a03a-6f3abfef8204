import os
import subprocess
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QTreeWidget, QTreeWidgetItem, QHeaderView, QMenu
from gxpy.gxengine.common import resource_path
from gxapp.public_data import color_print


class GxTreeManager:
    def __init__(self, tree_widget, data):
        self.tree_widget = tree_widget
        self.data = data
        self.setup_tree()

    def setup_tree(self):
        self.tree_widget.setColumnCount(1)                                              # 将树控件的列数设置为 1
        self.tree_widget.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)             # 设置水平滚动条的策略为仅在需要时显示滚动条
        self.tree_widget.header().setStretchLastSection(False)                          # 禁用最后一个头部分区的拉伸
        self.tree_widget.header().setSectionResizeMode(QHeaderView.ResizeToContents)    # 将分区大小模式设置为根据内容调整大小
        self.tree_widget.setHeaderLabels(["测试用例列表"])                                # 设置树控件标题的标签为 "测试用例列表"

    def init_tree_structure(self):
        """
        data 参数格式：
        self.data = {
            "chip" : "APUS",
            "test_case": {
                "RF发射测试": [
                    {"name":"RF发射测试1", "file":"./test/test1.py"},
                    {"name":"RF发射测试2", "file":"./test/test2.py"},
                    {"name":"RF发射测试3", "file":"./test/test3.py"}
                    ],
                "RF接收测试": [
                    {"name":"RF接收测试1", "file":"./test/test1.py"},
                    {"name":"RF接收测试2", "file":"./test/test2.py"},
                    {"name":"RF接收测试3", "file":"./test/test3.py"}
                    ],
                }
            }
        """
        # 清除旧的树结构
        self.tree_widget.clear()

        # 根据提供的数据构建树结构
        for parent_name, children in self.data["test_case"].items():
            parent_item = QTreeWidgetItem(self.tree_widget)
            parent_item.setText(0, parent_name)         # QTreeWidgetItem.setText(column,text) 设置文本名称为指定列的子节点item

            for child_name in children:
                child_item = QTreeWidgetItem()                                      # 创建一个新树控件子项
                child_item.setText(0, child_name["name"])                           # 设置子项的文本，其中 0 表示第一列（索引从 0 开始）
                child_item.setFlags(child_item.flags() | Qt.ItemIsUserCheckable)    # 设置子项的标志，使其可由用户勾选
                child_item.setCheckState(0, Qt.Unchecked)                           # 设置子项的初始勾选状态为未勾选
                parent_item.addChild(child_item)                                    # 将子项添加到父项中

        self.tree_widget.expandAll()                                                        # 展开树控件中的所有项
        self.tree_widget.header().setSectionResizeMode(QHeaderView.ResizeToContents)        # 将树控件头部的分区大小模式设置为根据内容调整大小

        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)                         # 设置树控件的上下文菜单策略，策略允许你创建和显示自定义上下文菜单
        self.tree_widget.customContextMenuRequested.connect(self.on_custom_context_menu)    # 信号会在用户右键单击树控件时发出，connect() 方法将该信号连接到 on_custom_context_menu 槽，该槽负责显示自定义上下文菜单

    def get_checked_items(self):
        """
        输出格式：
            checked_items = {
                "RF发射测试": [
                    {"name":"RF发射测试1", "file":"./test/test1.py"},
                    {"name":"RF发射测试3", "file":"./test/test3.py"}
                    ],
                "RF接收测试": [
                    {"name":"RF接收测试3", "file":"./test/test3.py"}
                    ],
                }
        """
        checked_items = {}
        for i in range(self.tree_widget.topLevelItemCount()):
            parent_item = self.tree_widget.topLevelItem(i)
            parent_name = parent_item.text(0)
            checked_items[parent_name] = []
            for j in range(parent_item.childCount()):
                child_item = parent_item.child(j)
                if child_item.checkState(0) == Qt.Checked:
                    test_case_data = self.data["test_case"][parent_name][j]
                    checked_items[parent_name].append(test_case_data)
            if not checked_items[parent_name]:  # Remove empty lists
                del checked_items[parent_name]
        return checked_items

    def set_checkbox_state(self, state):
        for i in range(self.tree_widget.topLevelItemCount()):
            parent_item = self.tree_widget.topLevelItem(i)
            for j in range(parent_item.childCount()):
                child_item = parent_item.child(j)
                # 根据state设置子节点的复选框是否可用
                if state:
                    child_item.setFlags(child_item.flags() | Qt.ItemIsUserCheckable)    # 启用子项的复选框
                else:
                    child_item.setFlags(child_item.flags() & ~Qt.ItemIsUserCheckable)   # 禁用子项的复选框

    def on_custom_context_menu(self, point):
        # 获取选中的项
        item = self.tree_widget.itemAt(point)                                       # 获取在给定点（point）下方的树控件项
        if item and item.parent():                                                  # 检查该项是否存在并且它是子项（即，它有父项）。此检查可确保你只对子项显示上下文菜单
            menu = QMenu()                                                          # 创建一个新的上下文菜单
            edit_action = menu.addAction("编辑文件")                                # 向菜单中添加一个名为 "编辑文件" 的动作
            action = menu.exec_(self.tree_widget.viewport().mapToGlobal(point))     # 在树控件的可视区域相对于全局坐标的给定点处显示上下文菜单，并等待用户选择一个动作
            if action == edit_action:                                               # 检查用户是否选择了 "编辑文件" 动作
                self.edit_file(item)                                                # 如果用户选择了 "编辑文件" 动作，则调用 edit_file 方法来编辑与该项关联的文件

    def edit_file(self, item):
        # 查找焦点选中，且将要右键编辑文件的路径
        # color_print(item.parent().indexOfChild(item))
        # color_print(item.text(0))
        # color_print(item.parent().text(0))
        point_case_name = item.text(0)                                                  # 焦点所在项目的用例名称
        point_case_parent_name = item.parent().text(0)                                  # 焦点所在项目的父项的名称（用例集名称）
        point_case_index = item.parent().indexOfChild(item)                             # 焦点所在项目在父项的treeWidget的index值
        case_item = self.data["test_case"][point_case_parent_name][point_case_index]
        color_print(case_item)
        if case_item["name"] == point_case_name:
            file_path = case_item["file"]
            self.open_file_in_editor(file_path)

    def edit_file_old(self, item):
        # 查找与项关联的文件
        for test_cases in self.data["test_case"].values():
            for test_case in test_cases:
                if test_case["name"] == item.text(0):
                    file_path = test_case["file"]
                    self.open_file_in_editor(file_path)
                    break

    def open_file_in_editor(self, file_path):
        # 根据操作系统选择编辑器
        if os.name == 'nt':  # Windows 系统
            editor_path = resource_path('bin/npp.8.6.4.portable.minimalist/notepad++.exe')
        else:  # Linux 系统
            editor_path = 'gedit'

        if os.path.exists(editor_path) or os.name != 'nt':
            # 使用编辑器打开文件
            subprocess.Popen([editor_path, file_path])
        else:
            print("编辑器在路径中未找到:", editor_path)
