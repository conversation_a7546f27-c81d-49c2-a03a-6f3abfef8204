#!/usr/bin/env python3
"""
测试脚本：验证macOS菜单栏修复
用于测试在macOS 10.15.7系统上菜单栏是否能正常工作
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QMenuBar, QAction, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

class TestMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("菜单栏测试 - macOS 10.15.7")
        self.setGeometry(100, 100, 800, 600)
        
        # 初始化菜单栏
        self.init_menu()
        
    def init_menu(self):
        """初始化菜单栏 - 使用与修复后相同的逻辑"""
        # 正确获取菜单栏对象
        self.menubar = self.menuBar()
        
        # macOS特殊处理
        if sys.platform == 'darwin':  # macOS
            # 先尝试使用原生macOS菜单栏
            try:
                self.menubar.setNativeMenuBar(True)
                # 在macOS上，确保菜单栏可见
                self.menubar.setVisible(True)
                print("✓ 原生macOS菜单栏设置成功")
            except Exception as e:
                print(f"✗ macOS菜单栏设置失败: {e}")
                # 如果原生菜单栏失败，则使用非原生模式
                self.menubar.setNativeMenuBar(False)
                print("✓ 切换到非原生菜单栏模式")
        
        # 创建文件菜单
        self.file_menu = self.menubar.addMenu("文件")
        open_action = self.file_menu.addAction("打开测试文件")
        open_action.triggered.connect(self.test_open_file)
        
        # 创建配置菜单
        self.config_menu = self.menubar.addMenu("配置")
        setting_action = self.config_menu.addAction("设置")
        setting_action.triggered.connect(self.test_settings)
        
        # 创建帮助菜单
        self.help_menu = self.menubar.addMenu("帮助")
        help_doc_action = self.help_menu.addAction("帮助文档")
        help_doc_action.triggered.connect(self.test_help)
        about_action = self.help_menu.addAction("关于")
        about_action.triggered.connect(self.test_about)
        
        print("✓ 菜单栏初始化完成")
        
    def test_open_file(self):
        """测试文件菜单功能"""
        QMessageBox.information(self, "测试", "文件菜单点击成功！\n这证明菜单栏修复有效。")
        print("✓ 文件菜单点击测试成功")
        
    def test_settings(self):
        """测试配置菜单功能"""
        QMessageBox.information(self, "测试", "配置菜单点击成功！")
        print("✓ 配置菜单点击测试成功")
        
    def test_help(self):
        """测试帮助菜单功能"""
        QMessageBox.information(self, "测试", "帮助菜单点击成功！")
        print("✓ 帮助菜单点击测试成功")
        
    def test_about(self):
        """测试关于菜单功能"""
        QMessageBox.information(self, "测试", "关于菜单点击成功！")
        print("✓ 关于菜单点击测试成功")

def main():
    """主函数"""
    print("开始测试macOS菜单栏修复...")
    print(f"当前系统: {sys.platform}")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # macOS特殊处理
    if sys.platform == 'darwin':
        try:
            app.setAttribute(Qt.AA_DontShowIconsInMenus, False)
            app.setAttribute(Qt.AA_NativeWindows, True)
            print("✓ macOS应用程序属性设置成功")
        except Exception as e:
            print(f"✗ macOS应用程序属性设置失败: {e}")
    
    # 创建主窗口
    window = TestMainWindow()
    window.show()
    
    print("\n测试说明:")
    print("1. 如果您在macOS上看到菜单栏出现在屏幕顶部，说明原生菜单栏工作正常")
    print("2. 如果菜单栏出现在窗口内，说明使用了非原生模式")
    print("3. 请尝试点击'文件'菜单中的'打开测试文件'来验证功能")
    print("4. 如果能成功点击并弹出对话框，说明修复有效")
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
