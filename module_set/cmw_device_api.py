import time

from module_set.device_base_api_class import DeviceBaseApi


class CmwApi(DeviceBaseApi):

    def __init__(self, ip: str, port: int):
        super().__init__(ip=ip, port=port)

    def cmw_device_hardcopy_set_screenshots_format(self, image_format: str):
        """
        指定通过 HCOPy:FILE、HCOPy:DATA?、HCOPy:INTerior:FILE 或 HCOPy:INTerior:DATA? 命令创建的截图格式。
            * full cmd format: ":HCOPy:DEVice:FORMat <image_format>"
            * short cmd format: ":HCOP:DEV:FORM <image_format>"
            * image_format:
                * BMP | JPG | PNG
            * Example:
                * HCOPy:DEVice:FORMat PNG
        """
        cmd = "HCOPy:DEVice:FORMat {}".format(image_format)
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        self.device_check_operation_complete()

    def cmw_device_hardcopy_query_captures_screenshot_block_data_format(self):
        """
        截图并以块数据格式返回结果，HCOPy:DATA 截取整个窗口，HCOPy:INTerior:DATA 只截取窗口内部。
        建议在发送此命令前 "打开 "显示屏，参见 SYSTem:DISPlay:UPDate。
            * full cmd format: ":HCOPy:DATA?"
            * short cmd format: ":HCOP:DATA?"
            * Return values:
                * dblock Data: Screenshot in 488.2 block data format
        """
        cmd = ":HCOPy:DATA?"
        new_cmd = self.cmd_line_ending_to_add_carriage_return_and_newline(cmd=cmd)
        self.send_data(data=new_cmd)
        time.sleep(0.1)
        data = self.recv_image_data()
        return data
