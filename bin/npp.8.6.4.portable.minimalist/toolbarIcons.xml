<?xml version="1.0" encoding="UTF-8" ?>
	<!--
	This file is for customizing your toolbar icons.
	
	To override the current toolbar icons, you need 2 things: this file and your icons set.
	Here are the instructions to customize your toolbar icons:
	
	1. Put this file ("toolbarIcons.xml") in the same folder as "config.xml" file (Note 1).
	2. Create a new folder "toolbarIcons" in the folder where you put "toolbarIcons.xml" file.
	3. Edit this file ("toolbarIcons.xml"): put the icon set name you want in "icoFolderName" attribute (Note 2).
	   For example: <ToolBarIcons icoFolderName="myAwesomeIcons" />
	4. Go into "toolbarIcons" folder and create a new folder with the exact name of the icon set name you provided in "icoFolderName".
	5. Put all your customized icons into "[toolbarIcons.xml's folder]\toolbarIcons\myAwesomeIcons\".
	6. Now it's the magic moment: Relaunch Notepad++ and you'll see your icon set instead of the default icons.
	
	Note:
	1. If you find the file "doLocalConf.xml" in the Notepad++ installed directory, you will find the "config.xml" in Notepad++ installed directory. Otherwise it should be in "%APPDATA%\Notepad++\" directory.
	2. if "icoFolderName" value is an empty string, the path of icons will be in "[toolbarIcons.xml's folder]\toolbarIcons\default\" folder.
	
	Each replacing icon (45 icons) has the fixed and specific name:
	
	1   new.ico
	2   open.ico
	3   save.ico                save_disabled.ico
	4   save-all.ico            save-all_disabled.ico
	5   close.ico
	6   close-all.ico
	7   print.ico
	8   cut.ico                 cut_disabled.ico
	9   copy.ico                copy_disabled.ico
	10  paste.ico               paste_disabled.ico
	11  undo.ico                undo_disabled.ico
	12  redo.ico                redo_disabled.ico
	13  find.ico
	14  replace.ico
	15  zoom-in.ico
	16  zoom-out.ico
	17  sync-vertical.ico
	18  sync-horizontal.ico
	19  word-wrap.ico
	20  all-chars.ico
	21  indent-guide.ico
	22  udl-dlg.ico
	23  doc-map.ico
	24  doc-list.ico
	25  function-list.ico
	26  folder-as-workspace.ico
	27  monitoring.ico          monitoring_disabled.ico
	28  record.ico              record_disabled.ico
	29  stop-record.ico         stop-record_disabled.ico
	30  playback.ico            playback_disabled.ico
	31  playback-multiple.ico   playback-multiple_disabled.ico
	32  save-macro.ico          save-macro_disabled.ico
	
	It's not necessary to have all complete set (45 icons). The absent icons just won't be substituted.
	-->
<NotepadPlus>
    <ToolBarIcons icoFolderName="" />
</NotepadPlus>